import { ClientRequest, ClientResponse } from '@moxo/proto';
import { AjaxMethod, IRequestParam, sendRequest } from '../network/ajax';
import sdkConfig from './config';

export function sendBizServerRequest(
  body: ClientRequest,
  opts: IRequestParam,
): Promise<ClientResponse> {
  let requestUrl = window.location.origin + sdkConfig.servicePath;
  if (body.object?.board) {
    requestUrl += '/board';
  } else if (body.object?.user) {
    requestUrl += '/user';
  } else if (body.object?.group) {
    requestUrl += '/group';
  }
  return sendRequest(requestUrl, { method: AjaxMethod.POST, ...opts }, body);
}
